// ==============================================
// Copyright (c) 2025 reall3d.com, MIT license
// ==============================================
import { Camera, PerspectiveCamera, Vector3 } from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { Reall3dViewerOptions } from '../viewer/Reall3dViewerOptions';
import { isMobile } from '../utils/consts/GlobalConstants';

/**
 * 旋转控制器
 */
export class CameraControls extends OrbitControls {
    private _mobileAdjusted = false; // 添加标志防止重复调用移动端调整
    private _originalMinDistance: number = 0.1; // 保存原始的最小距离
    private _originalMaxDistance: number = 1000; // 保存原始的最大距离

    constructor(opts: Reall3dViewerOptions) {
        const camera: Camera = opts.camera;
        super(camera, opts.renderer.domElement);

        const that = this;
        that.dampingFactor = 0.1;
        that.rotateSpeed = 0.4;
        that.autoRotateSpeed = 0.5; // 设置自动旋转速度（度/秒）
        that.updateByOptions(opts);
    }

    public updateByOptions(opts: Reall3dViewerOptions = {}) {
        if (!opts) return;
        const that = this;

        opts.enableDamping !== undefined && (that.enableDamping = opts.enableDamping);
        if (opts.autoRotate !== undefined) {
            that.autoRotate = opts.autoRotate;
            console.log('[CameraControls.updateByOptions] 设置 autoRotate:', that.autoRotate);
        }
        if (opts.autoRotateSpeed !== undefined) {
            that.autoRotateSpeed = opts.autoRotateSpeed;
            console.log('[CameraControls.updateByOptions] 设置 autoRotateSpeed:', that.autoRotateSpeed);
        }
        opts.enableZoom !== undefined && (that.enableZoom = opts.enableZoom);
        opts.enableRotate !== undefined && (that.enableRotate = opts.enableRotate);
        opts.enablePan !== undefined && (that.enablePan = opts.enablePan);
        if (opts.minDistance !== undefined) {
            that.minDistance = opts.minDistance;
            that._originalMinDistance = opts.minDistance; // 保存原始值
            console.log('[CameraControls.updateByOptions] 设置 minDistance:', that.minDistance);
        }
        if (opts.maxDistance !== undefined) {
            that.maxDistance = opts.maxDistance;
            that._originalMaxDistance = opts.maxDistance; // 保存原始值
            console.log('[CameraControls.updateByOptions] 设置 maxDistance:', that.maxDistance);
        }
        opts.minPolarAngle !== undefined && (that.minPolarAngle = opts.minPolarAngle);
        opts.maxPolarAngle !== undefined && (that.maxPolarAngle = opts.maxPolarAngle);

        opts.fov !== undefined && ((that.object as PerspectiveCamera).fov = opts.fov);
        opts.near !== undefined && ((that.object as PerspectiveCamera).near = opts.near);
        opts.far !== undefined && ((that.object as PerspectiveCamera).far = opts.far);
        opts.position && that.object.position.fromArray(opts.position);
        opts.lookAt && that.target.fromArray(opts.lookAt);
        opts.lookUp && that.object.up.fromArray(opts.lookUp);

        // 处理第一人称模式设置
        that.updateControlMode(opts);

        // 移动端相机距离调整（只调用一次）
        //that.adjustForMobile();
        that.updateRotateAxis();
        that.update();
    }

    /**
     * 更新控制模式
     * @param opts 选项
     */
    public updateControlMode(opts: Reall3dViewerOptions) {
        const that = this;
        const useCustomControl = opts.useCustomControl === true;

        console.log('[updateControlMode] 当前相机位置:', that.object.position.toArray());
        console.log('[updateControlMode] 当前目标位置:', that.target.toArray());

        // 当启用自定义控制模式时，禁用默认的旋转控制
        if (useCustomControl) {
            console.log('[updateControlMode] 启用第一人称模式');

            // 断开OrbitControls的事件监听器
            that.disconnect();

            // 完全禁用所有默认控制
            that.enabled = false; // 整个控制器禁用

            // 禁用所有交互
            that.enableRotate = false; // 禁用旋转
            that.enablePan = false; // 禁用平移
            that.enableZoom = false; // 禁用缩放
            that.enableDamping = false; // 禁用阻尼效果
            that.autoRotate = false; // 禁用自动旋转

            // 设置一个固定的距离，使用当前相机到目标的实际距离
            const currentDistance = this.getDistance();
            that.minDistance = currentDistance;
            that.maxDistance = currentDistance;
        } else {
            console.log('[updateControlMode] 禁用第一人称模式，恢复轨道控制');

            // 恢复默认行为
            that.enabled = true; // 启用控制器
            that.enableRotate = opts.enableRotate !== undefined ? opts.enableRotate : true;
            that.enablePan = opts.enablePan !== undefined ? opts.enablePan : true;
            that.enableZoom = opts.enableZoom !== undefined ? opts.enableZoom : true;
            that.enableDamping = opts.enableDamping !== undefined ? opts.enableDamping : true;
            that.autoRotate = opts.autoRotate !== undefined ? opts.autoRotate : false;
            console.log('[updateControlMode] 恢复轨道控制模式，autoRotate设置为:', that.autoRotate);

            // 恢复默认距离限制
            that.minDistance = opts.minDistance !== undefined ? opts.minDistance : that._originalMinDistance;
            that.maxDistance = opts.maxDistance !== undefined ? opts.maxDistance : that._originalMaxDistance;
            console.log('[updateControlMode] 恢复距离限制 - minDistance:', that.minDistance, 'maxDistance:', that.maxDistance);

            // 恢复极角限制，防止在轨道模式下看到模型底部
            that.minPolarAngle = opts.minPolarAngle !== undefined ? opts.minPolarAngle : 0;
            that.maxPolarAngle = opts.maxPolarAngle !== undefined ? opts.maxPolarAngle : Math.PI / 2;

            // 重新连接事件监听器
            that.connect();

            console.log('[updateControlMode] 已重新连接OrbitControls事件监听器');
        }

        console.log('[updateControlMode] 更新后相机位置:', that.object.position.toArray());
        console.log('[updateControlMode] 更新后目标位置:', that.target.toArray());
    }

    /**
     * 获取相机到目标的距离
     */
    public getDistance(): number {
        // @ts-ignore - 访问私有属性
        return this._spherical?.radius || 1;
    }

    /**
     * 移动端相机距离调整（只调用一次）
     */
    public adjustForMobile(): void {
        // @ts-ignore
        if (isMobile && !this._mobileAdjusted && this._dollyOut) {
            // @ts-ignore
            this._dollyOut(0.75); // 手机适当缩小
            this._mobileAdjusted = true;
            console.log('[CameraControls] 移动端相机距离已调整');
        }
    }

    /**
     * 更新旋转轴
     */
    public updateRotateAxis() {
        // @ts-ignore
        this._quat?.setFromUnitVectors?.(this.object.up, new Vector3(0, 1, 0));
        // @ts-ignore
        this._quatInverse = this._quat?.clone?.()?.invert?.();
    }

    /**
     * 覆盖OrbitControls的update方法，在第一人称模式下防止相机位置变化
     * @returns 是否进行了更新
     */
    public update(): boolean {
        // 检查是否在第一人称模式（即控制器被禁用）
        if (this.enabled === false) {
            // 在第一人称模式下，不调用父类update方法，防止相机位置变化
            return false;
        }

        // 在正常模式下，调用父类的update方法
        return super.update();
    }

    /**
     * 重新连接OrbitControls的事件监听器
     * 这个方法在从第一人称模式切换回轨道控制模式时调用
     */
    public connect(): void {
        // 调用父类的connect方法来重新绑定所有事件监听器
        super.connect();
        console.log('[CameraControls.connect] 已重新连接事件监听器');
    }

    /**
     * 断开OrbitControls的事件监听器
     * 这个方法在切换到第一人称模式时调用
     */
    public disconnect(): void {
        // 调用父类的disconnect方法来移除所有事件监听器
        super.disconnect();
        console.log('[CameraControls.disconnect] 已断开事件监听器');
    }
}
