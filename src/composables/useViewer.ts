import { ref, onMounted, onUnmounted, watch, shallowRef, unref, type Ref } from 'vue'
import { Reall3dViewer } from '@reall3d/reall3dviewer'
import * as THREE from 'three'
import type { ViewerConfig, CameraConfig } from '@/types/project'
import type { Reall3dViewerOptions } from '@reall3d/reall3dviewer'

export function useViewer(
  config: ViewerConfig = { useCustomControl: false, disableTransitionEffect: true },
  animTracks: any[] = [],
  skyboxTextureName: Ref<string> | string = 'default' as string,
) {
  // 响应式状态
  const viewer = shallowRef<Reall3dViewer | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const modelUrl = ref<string>('')
  const isTourActive = ref(false)
  const tourAnimationId = ref<number | null>(null)
  const skybox = shallowRef<THREE.Mesh | null>(null) // 使用 shallowRef

  // 默认配置
  const defaultConfig: ViewerConfig = {
    debugMode: false,
    useCustomControl: false,
    disableTransitionEffectOnLoad: false,
    transitionEffect: 1,
    transitionAnimDuration: 10000,
    qualityLevel: 9,
    autoRotate: true,  // 修正：使用正确的属性名 autoRotate 而不是 enableAutoRotate
    autoRotateSpeed: 0.5,  // 默认自动旋转速度
    minDistance: 3,
    maxDistance: 15,
    enablePan: false,  // 关闭双指移动功能
    ...config  // 用户配置应该在最后，以便覆盖默认配置
  }

  /**
   * 获取Viewer的Three.js场景实例
   */
  const getViewerScene = (): THREE.Scene | null => {
    if (!viewer.value) return null

    try {
      const viewerOptions = viewer.value.options() as Reall3dViewerOptions & { scene: THREE.Scene }
      return viewerOptions.scene
    } catch (err) {
      console.error('获取场景失败:', err)
      return null
    }
  }

  // 修改材质设置
  const initSkybox = () => {
    if (!viewer.value) return

    const scene = getViewerScene()
    if (!scene) {
      console.error('无法获取场景实例，无法初始化天空球')
      return
    }

    // 移除已有的天空球
    if (skybox.value && scene.children.includes(skybox.value)) {
      disposeSkyboxResources(skybox.value)
      scene.remove(skybox.value)
      skybox.value = null
    }

    // 创建天空球几何体
    const radius = 600 // 天空球半径, 选择过大会超出区域进而不显示 选择1000会导致渲染黑影
    const geometry = new THREE.SphereGeometry(radius, 64, 64)

    // 加载天空球纹理
    const textureLoader = new THREE.TextureLoader()
    //const textureLoader = new RGBELoader()
    const currentSkyboxName = unref(skyboxTextureName)
    const textureUrl = `/skyboxes/${currentSkyboxName}.png`

    textureLoader.load(
      textureUrl,
      (texture) => {
        const material = new THREE.MeshBasicMaterial({
          map: texture,
          side: THREE.BackSide,
          depthWrite: false, // 不写入深度缓冲，避免干扰点云
          depthTest: true, // 参与深度测试，但不修改缓冲
          transparent: false, // 禁用透明，避免混合问题
        })

        const newSkybox = new THREE.Mesh(geometry, material)
        newSkybox.renderOrder = -1000 // 优先渲染（最低渲染顺序）
        newSkybox.rotation.set(0, -2*Math.PI/5, 0)
        skybox.value = newSkybox
        scene.add(skybox.value)
        console.log(`天空球 ${currentSkyboxName} 加载成功`)
      },
      undefined,
      (err) => {
        console.error(`天空球纹理加载失败 (${textureUrl})`, err)
        if (currentSkyboxName !== 'default') {
          // 如果是响应式引用，需要更新其值；如果是字符串，则无法更新
          console.warn('天空球纹理加载失败，但无法自动回退到默认纹理')
        }
      },
    )
  }

  /**
   * 销毁天空球资源（解决材质销毁报错的核心方法）
   */
  const disposeSkyboxResources = (mesh: THREE.Mesh) => {
    // 1. 销毁几何体
    mesh.geometry.dispose()

    // 2. 处理材质（可能是单个材质或材质数组）
    const materials = Array.isArray(mesh.material) ? mesh.material : [mesh.material]

    materials.forEach((material) => {
      // 处理纹理（先判断map是否存在）
      if (material instanceof THREE.MeshBasicMaterial && material.map) {
        material.map.dispose() // 仅在map存在时调用dispose
      }
      // 销毁材质本身
      if (typeof material.dispose === 'function') {
        material.dispose()
      }
    })
  }

  /**
   * 初始化Viewer
   */
  const initViewer = async (): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null

      // 检查是否有计算好的相机移动速度
      if (defaultConfig.cameraMoveSpeed) {
        console.log(`[useViewer] 使用计算得到的相机移动速度:`, defaultConfig.cameraMoveSpeed)
      } else {
        console.log(`[useViewer] 使用默认相机移动速度:`, defaultConfig.cameraMoveSpeed)
      }

      viewer.value = new Reall3dViewer(defaultConfig)
      console.log('Reall3dViewer初始化成功', defaultConfig)
      console.log('[useViewer] autoRotate配置:', defaultConfig.autoRotate)
      console.log('[useViewer] useCustomControl配置:', defaultConfig.useCustomControl)

      // 确保自动旋转被正确启用
      setTimeout(() => {
        if (viewer.value && defaultConfig.autoRotate && !defaultConfig.useCustomControl) {
          console.log('[useViewer] 强制启动自动旋转')
          viewer.value.options({ autoRotate: true })
        }
      }, 1000)

      setTimeout(initSkybox, 800) // 延迟初始化天空球
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化失败'
      console.error('Reall3dViewer初始化失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 加载模型
   */
  const loadModel = async (url: string): Promise<void> => {
    if (!viewer.value) {
      error.value = 'Viewer未初始化'
      return
    }
    if (!url) {
      error.value = '模型URL不能为空'
      return
    }
    try {
      isLoading.value = true
      error.value = null
      modelUrl.value = url
      await viewer.value.addModel(url)
      console.log('模型加载成功:', url)
      if (skybox.value) {
        skybox.value.visible = true
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '模型加载失败'
      console.error('模型加载失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 重置Viewer
   */
  const resetViewer = async (newConfig?: ViewerConfig): Promise<void> => {
    destroyViewer()
    if (newConfig) {
      Object.assign(defaultConfig, newConfig)
    }
    await initViewer()
    if (modelUrl.value) {
      await loadModel(modelUrl.value)
    }
  }

  /**
   * 销毁Viewer
   */
  const destroyViewer = (): void => {
    if (viewer.value) {
      try {
        // 销毁天空球
        if (skybox.value) {
          const scene = getViewerScene()
          if (scene && scene.children.includes(skybox.value)) {
            disposeSkyboxResources(skybox.value) // 使用专用销毁方法
            scene.remove(skybox.value)
          }
          skybox.value = null
        }

        // 销毁Viewer实例
        viewer.value.dispose()
        viewer.value = null
        modelUrl.value = ''
        console.log('Viewer已销毁')
      } catch (err) {
        console.error('Viewer销毁失败:', err)
      }
    }
  }

  /**
   * 更新Viewer选项
   */
  const updateOptions = (options: Record<string, any>): void => {
    if (!viewer.value) return
    try {
      viewer.value.options(options)
      console.log('Viewer选项更新成功:', options)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '选项更新失败'
      console.error('Viewer选项更新失败:', err)
    }
  }

  /**
   * 更新相机配置
   */
  const updateCamera = (cameraConfig: CameraConfig): void => {
    if (!viewer.value) return
    try {
      const { position, lookAt, lookUp, fov, near, far } = cameraConfig
      const cameraOptions: Record<string, any> = {}
      if (position) cameraOptions.position = position
      if (lookAt) cameraOptions.lookAt = lookAt
      if (lookUp) cameraOptions.lookUp = lookUp
      if (fov !== undefined) cameraOptions.fov = fov
      if (near !== undefined) cameraOptions.near = near || 0.1
      if (far !== undefined) cameraOptions.far = far || 500
      viewer.value.options(cameraOptions)
      console.log('相机配置更新成功:', cameraOptions)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '相机配置更新失败'
      console.error('相机配置更新失败:', err)
    }
  }

  /**
   * 重置相机到初始位置
   */
  const resetCamera = (): void => {
    if (!viewer.value) return
    try {
      const { position, lookAt, lookUp } = defaultConfig
      if (position || lookAt || lookUp) {
        updateCamera({ position, lookAt, lookUp })
      }
      console.log('相机已重置到初始位置')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '相机重置失败'
      console.error('相机重置失败:', err)
    }
  }

  /**
   * 开始自动循环漫游
   */
  const startAutoTour = (): void => {
    if (!viewer.value || !animTracks || animTracks.length === 0 || isTourActive.value) {
      return
    }
    try {
      const track = animTracks[0]
      const { duration = 6, frameRate = 30, keyframes = [] } = track
      if (keyframes.length === 0) {
        console.warn('动画轨道没有关键帧数据')
        return
      }
      isTourActive.value = true
      console.log(`[AutoTour] 开始自动循环漫游，时长: ${duration}秒，帧率: ${frameRate}fps`)
      const totalFrames = duration * frameRate
      animateCamera(keyframes, totalFrames, duration, true)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '自动漫游失败'
      console.error('自动漫游失败:', err)
      isTourActive.value = false
    }
  }

  /**
   * 停止漫游
   */
  const stopTour = (): void => {
    if (isTourActive.value) {
      isTourActive.value = false
      if (tourAnimationId.value) {
        cancelAnimationFrame(tourAnimationId.value)
        tourAnimationId.value = null
      }
      console.log('[AutoTour] 漫游已停止')
    }
  }

  /**
   * 切换自动旋转状态
   */
  const toggleAutoRotate = () => {
    if (!viewer.value) {
      console.warn('[useViewer] Viewer未初始化，无法切换自动旋转')
      return false
    }

    try {
      const currentOptions = viewer.value.options() as any
      const currentAutoRotate = currentOptions.autoRotate || false
      const newAutoRotate = !currentAutoRotate

      // 只有在非自定义控制模式下才能启用自动旋转
      if (newAutoRotate && currentOptions.useCustomControl) {
        console.warn('[useViewer] 自定义控制模式下无法启用自动旋转')
        return false
      }

      viewer.value.options({
        autoRotate: newAutoRotate,
        autoRotateSpeed: defaultConfig.autoRotateSpeed || 2.0
      })

      console.log(`[useViewer] 自动旋转已${newAutoRotate ? '启用' : '禁用'}，速度:`, defaultConfig.autoRotateSpeed || 2.0)
      return newAutoRotate
    } catch (error) {
      console.error('[useViewer] 切换自动旋转失败:', error)
      return false
    }
  }

  /**
   * 获取当前自动旋转状态
   */
  const getAutoRotateStatus = (): boolean => {
    if (!viewer.value) return false
    try {
      const currentOptions = viewer.value.options() as any
      return currentOptions.autoRotate || false
    } catch (error) {
      console.error('[useViewer] 获取自动旋转状态失败:', error)
      return false
    }
  }

  /**
   * 获取当前自动漫游状态
   */
  const getTourStatus = (): boolean => {
    return isTourActive.value
  }

  /**
   * 执行相机动画
   */
  const animateCamera = (
    keyframes: any[],
    totalFrames: number,
    duration: number,
    loop = false,
  ): void => {
    const startTime = Date.now()
    const animate = () => {
      if (!isTourActive.value && loop) return
      const elapsed = (Date.now() - startTime) / 1000
      let progress = elapsed / duration
      if (loop) progress = progress % 1
      else progress = Math.min(progress, 1)

      if (progress >= 1 && !loop) {
        const lastFrame = keyframes[keyframes.length - 1]
        if (lastFrame.position && lastFrame.target) {
          updateCamera({ position: lastFrame.position, lookAt: lastFrame.target })
        }
        console.log('[Tour] 漫游完成')
        return
      }

      const currentFrame = progress * totalFrames
      const interpolatedCamera = interpolateKeyframes(keyframes, currentFrame)
      if (interpolatedCamera) {
        updateCamera({ position: interpolatedCamera.position, lookAt: interpolatedCamera.target })
      }
      tourAnimationId.value = requestAnimationFrame(animate)
    }
    animate()
  }

  /**
   * 关键帧插值
   */
  const interpolateKeyframes = (keyframes: any[], currentFrame: number) => {
    let prevFrame = keyframes[0]
    let nextFrame = keyframes[keyframes.length - 1]
    for (let i = 0; i < keyframes.length - 1; i++) {
      if (currentFrame >= keyframes[i].times && currentFrame <= keyframes[i + 1].times) {
        prevFrame = keyframes[i]
        nextFrame = keyframes[i + 1]
        break
      }
    }
    if (prevFrame.times === nextFrame.times) return prevFrame
    const t = (currentFrame - prevFrame.times) / (nextFrame.times - prevFrame.times)
    const position = [
      prevFrame.position[0] + (nextFrame.position[0] - prevFrame.position[0]) * t,
      prevFrame.position[1] + (nextFrame.position[1] - prevFrame.position[1]) * t,
      prevFrame.position[2] + (nextFrame.position[2] - prevFrame.position[2]) * t,
    ]
    const target = [
      prevFrame.target[0] + (nextFrame.target[0] - prevFrame.target[0]) * t,
      prevFrame.target[1] + (nextFrame.target[1] - prevFrame.target[1]) * t,
      prevFrame.target[2] + (nextFrame.target[2] - prevFrame.target[2]) * t,
    ]
    return { position, target }
  }

  // 监听配置变化
  watch(
    () => config,
    (newConfig) => {
      if (viewer.value && newConfig) updateOptions(newConfig)
    },
    { deep: true },
  )

  // 监听天空球纹理变化
  watch(
    () => unref(skyboxTextureName),
    () => {
      if (viewer.value) initSkybox()
    },
  )

  // 组件生命周期
  onMounted(() => initViewer())
  onUnmounted(() => destroyViewer())

  return {
    // 返回 shallowRef 的 value，明确告知使用者这是一个非响应式对象
    viewer: viewer.value,
    skybox: skybox.value,
    isLoading,
    error,
    modelUrl,
    initViewer,
    loadModel,
    updateOptions,
    updateCamera,
    resetCamera,
    destroyViewer,
    resetViewer,
    startAutoTour,
    stopTour,
    toggleAutoRotate,
    getAutoRotateStatus,
    getTourStatus,
  }
}
